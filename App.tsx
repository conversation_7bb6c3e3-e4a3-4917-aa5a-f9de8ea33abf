import { StatusBar, ActivityIndicator, StyleSheet } from 'react-native';
import React, { useEffect } from 'react';
import { NavigationContainer, useNavigation } from '@react-navigation/native';
import {
  createNativeStackNavigator,
  NativeStackNavigationProp,
} from '@react-navigation/native-stack';
import Home from './src/pages/Home';
import Settings from './src/pages/Settings';
import CheckPermission from './src/components/CheckPermission';
import { RootStack } from './src/types';
import { useDevicePermissions } from './src/hooks/useDevicePermissions';

const Stack = createNativeStackNavigator<RootStack>();

function AppContent() {
  const {
    audioPermission,
    cameraPermission,
    storagePermission,
    locationPermission,
    isLoading,
  } = useDevicePermissions();

  const hasAllPermissions =
    audioPermission === 'granted' &&
    cameraPermission === 'granted' &&
    storagePermission === 'granted' &&
    locationPermission === 'granted';

  if (isLoading) {
    return <ActivityIndicator size="large" style={styles.loadingIndicator} />;
  }

  return (
    <Stack.Navigator
      initialRouteName={hasAllPermissions ? 'home' : 'permissions'}
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen name="home" component={Home} />
      <Stack.Screen name="settings" component={Settings} />
      <Stack.Screen name="permissions" component={CheckPermission} />
    </Stack.Navigator>
  );
}

function App() {
  return (
    <NavigationContainer>
      <StatusBar barStyle="light-content" />
      <AppContent />
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  loadingIndicator: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
});

export default App;
