import { View, Pressable, StyleSheet, Text } from 'react-native';
import { Ionicons } from '@react-native-vector-icons/ionicons';
import { useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../localStroage/asyncStorage';
import { useIsFocused } from '@react-navigation/core';

interface CameraHeaderProps {
  enableLocation: boolean;
  toggleLocation: () => void;
  toggleCamera: () => void;
  toggleFlash: () => void;
  flashMode: 'on' | 'off';
  cameraPosition: 'back' | 'front';
  hasFlash: boolean | undefined;
  recording: boolean;
  navigateToSettings: () => void;
}
export default function CameraHeader({
  toggleCamera,
  toggleLocation,
  toggleFlash,
  enableLocation,
  flashMode,
  cameraPosition,
  hasFlash,
  recording,
  navigateToSettings,
}: CameraHeaderProps) {
  const [resolution, setResolution] = useState('auto');
  const isFocussed = useIsFocused();

  useEffect(() => {
    AsyncStorage.getItem(STORAGE_KEYS.RESOLUTION_KEY).then(theme => {
      if (theme) {
        setResolution(theme);
      }
    });
  }, [isFocussed]);
  return (
    <View style={styles.mainContainer}>
      <Pressable
        style={[styles.icon, styles.setting]}
        onPress={navigateToSettings}
        disabled={recording}
      >
        <Ionicons name="settings-outline" size={32} color="#fff" />
      </Pressable>

      <View style={styles.resolutionContainer}>
        <Text style={styles.resolutionText}>{resolution.toUpperCase()}</Text>
      </View>

      <View style={styles.gap}>
        <Pressable
          style={styles.icon}
          onPress={toggleLocation}
          disabled={recording}
        >
          <Ionicons
            name={enableLocation ? 'location' : 'location-outline'}
            size={32}
            color={enableLocation ? '#77f330ff' : '#fff'}
          />
        </Pressable>
        <Pressable style={styles.icon} onPress={toggleCamera}>
          <Ionicons
            name={
              cameraPosition === 'back'
                ? 'camera-reverse'
                : 'camera-reverse-outline'
            }
            size={32}
            color="#fff"
          />
        </Pressable>
        {hasFlash && !recording && (
          <Pressable style={styles.icon} onPress={toggleFlash}>
            <Ionicons
              name={flashMode === 'on' ? 'flash' : 'flash-off'}
              size={32}
              color="#fff"
            />
          </Pressable>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    paddingHorizontal: 8,
    position: 'absolute',
    top: 28,
    zIndex: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  icon: {
    padding: 8,
    backgroundColor: '#00000086',
    borderRadius: 100,
  },
  setting: {
    alignSelf: 'flex-start',
  },
  gap: {
    gap: 12,
  },
  resolutionContainer: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  resolutionText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});
