import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Pressable,
  ScrollView,
  useColorScheme,
  ActivityIndicator,
} from 'react-native';
import { useDevicePermissions } from '../hooks/useDevicePermissions';
import { Ionicons } from '@react-native-vector-icons/ionicons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../localStroage/asyncStorage';
import { ColorScheme, RootStack } from '../types';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

const PermissionCard = React.memo(
  ({
    title,
    description,
    icon,
    onPress,
    styles,
  }: {
    title: string;
    description: string;
    icon: React.ComponentProps<typeof Ionicons>['name'];
    onPress: () => void;
    styles: ReturnType<typeof getStyles>;
  }) => (
    <View style={styles.card}>
      <Ionicons name={icon} size={32} style={styles.icon} />
      <View style={styles.textContainer}>
        <Text style={styles.permissionTitle}>{title}</Text>
        <Text style={styles.permissionDescription}>{description}</Text>
      </View>
      <Pressable style={styles.grantButton} onPress={onPress}>
        <Text style={styles.grantButtonText}>Grant</Text>
      </Pressable>
    </View>
  ),
);
export default function CheckPermission() {
  const {
    audioPermission,
    locationPermission,
    storagePermission,
    cameraPermission,
    requestCameraPermission,
    requestAudioPermission,
    requestLocationPermission,
    requestStoragePermission,
  } = useDevicePermissions();

  const systemTheme = useColorScheme() ?? 'light';
  const [theme, setTheme] = useState<ColorScheme>(systemTheme);
  const [loading, setLoading] = useState(true);

  const { reset } = useNavigation<NativeStackNavigationProp<RootStack>>();

  useEffect(() => {
    (async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(STORAGE_KEYS.THEME_KEY);
        if (savedTheme === 'light' || savedTheme === 'dark') {
          setTheme(savedTheme);
        }
      } catch (error) {
        console.error('Error loading theme:', error);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  const styles = getStyles(theme);

  const permissions = [
    {
      key: 'camera',
      status: cameraPermission,
      request: requestCameraPermission,
      title: 'Camera Permission',
      description: 'Needed to capture photos and videos.',
      icon: 'camera-outline' as const,
    },
    {
      key: 'audio',
      status: audioPermission,
      request: requestAudioPermission,
      title: 'Microphone Permission',
      description: 'Needed to record audio with your videos.',
      icon: 'mic-outline' as const,
    },
    {
      key: 'location',
      status: locationPermission,
      request: requestLocationPermission,
      title: 'Location Permission',
      description: 'Optional: to geotag your videos.',
      icon: 'location-outline' as const,
    },
    {
      key: 'storage',
      status: storagePermission,
      request: requestStoragePermission,
      title: 'Storage Permission',
      description: 'Needed to save videos to your gallery.',
      icon: 'folder-outline' as const,
    },
  ];

  const pendingPermissions = permissions.filter(p => p.status !== 'granted');
  const allGranted = pendingPermissions.length === 0;

  const handleContinue = () => {
    reset({
      index: 0,
      routes: [{ name: 'home' }],
    });
  };

  return (
    <View style={styles.container}>
      <Ionicons
        name="shield-checkmark-outline"
        size={64}
        style={styles.mainIcon}
      />
      <Text style={styles.welcome}>Welcome to vCam</Text>
      <Text style={styles.subtitle}>
        To get started, please grant the following permissions.
      </Text>

      <ScrollView contentContainerStyle={styles.permissionsContainer}>
        {pendingPermissions.map(permission => (
          <PermissionCard
            key={permission.key}
            title={permission.title}
            description={permission.description}
            icon={permission.icon}
            onPress={permission.request}
            styles={styles}
          />
        ))}
      </ScrollView>

      {allGranted && (
        <Pressable style={styles.themeButton} onPress={handleContinue}>
          <Text style={[styles.themeButtonText, styles.continue]}>
            Continue
          </Text>
        </Pressable>
      )}
    </View>
  );
}

const getStyles = (theme: ColorScheme) => {
  const isDark = theme === 'dark';
  const colors = {
    background: isDark ? '#000' : '#F2F2F7',
    text: isDark ? '#FFF' : '#000',
    subtitle: isDark ? '#A9A9A9' : '#6D6D72',
    card: isDark ? '#1C1C1E' : '#FFF',
    primary: isDark ? '#4CAF50' : '#007AFF',
    separator: isDark ? '#38383A' : '#E5E5EA',
  };

  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: 16,
      paddingTop: 80,
      backgroundColor: colors.background,
    },
    mainIcon: {
      color: colors.primary,
      alignSelf: 'center',
      marginBottom: 20,
    },
    welcome: {
      fontSize: 32,
      fontWeight: 'bold',
      textAlign: 'center',
      color: colors.text,
    },
    subtitle: {
      fontSize: 16,
      textAlign: 'center',
      color: colors.subtitle,
      marginTop: 8,
      marginBottom: 20,
    },
    themeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'center',
      backgroundColor: colors.primary,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 8,
      marginBottom: 20,
    },
    themeButtonText: {
      color: 'white',
      marginLeft: 8,
      fontWeight: '600',
    },
    permissionsContainer: {
      gap: 12,
      paddingBottom: 40,
    },
    card: {
      backgroundColor: colors.card,
      borderRadius: 12,
      padding: 16,
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: StyleSheet.hairlineWidth,
      borderColor: colors.separator,
    },
    icon: {
      color: colors.primary,
      marginRight: 16,
    },
    textContainer: {
      flex: 1,
    },
    permissionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
    },
    permissionDescription: {
      fontSize: 12,
      color: colors.subtitle,
      marginTop: 2,
    },
    grantButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      marginLeft: 10,
    },
    grantButtonText: {
      color: 'white',
      fontWeight: 'bold',
      fontSize: 14,
    },
    continue: {
      width: '100%',
      textAlign: 'center',
      fontSize: 18,
    },
  });
};
