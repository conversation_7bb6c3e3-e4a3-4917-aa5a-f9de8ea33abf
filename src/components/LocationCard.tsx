import { View, Text, ActivityIndicator } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useState } from 'react';
import { useDevicePermissions } from '../hooks/useDevicePermissions';
import Geolocation from 'react-native-geolocation-service';
import { getAddress } from '../utils/api';
import { AddressResponseType } from '../types';
import { getCurrentTimestamp } from '../utils/helper';

export default function LocationCard() {
  const { locationPermission } = useDevicePermissions();
  const [address, setAddress] = useState<AddressResponseType | null>(null);

  const fetchLocation = useCallback(() => {
    try {
      if (locationPermission === 'granted') {
        Geolocation.getCurrentPosition(
          async position => {
            const { latitude, longitude } = position.coords;

            try {
              const response = await getAddress(latitude, longitude);

              setAddress(response);
            } catch (error) {
              console.log(error, 'api error');
            }
          },
          error => {
            console.log('Error getting location:', error);
          },
          { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
        );
      }
    } catch (error) {
      console.log(error);
    }
  }, [locationPermission]);
  useFocusEffect(fetchLocation);

  return (
    <>
      <View
        style={{
          padding: 12,
          backgroundColor: '#00000086',
          borderRadius: 4,
        }}
      >
        {address ? (
          <>
            <Text style={{ color: '#ffffff' }}>{getCurrentTimestamp()}</Text>
            <Text style={{ color: '#ffffff' }}>{address.display_name}</Text>
            <Text style={{ color: '#ffffff' }}>
              {`Lat: ${address.lat}  Lon: ${address.lon}`}
            </Text>
          </>
        ) : (
          <ActivityIndicator size="large" color="#ffffff" />
        )}
      </View>
    </>
  );
}
