import React, { useEffect, useState } from 'react';
import CheckPermission from '../components/CheckPermission';
import { useDevicePermissions } from '../hooks/useDevicePermissions';
import { StackActions } from '@react-navigation/core';

export default function permission() {
  const {
    audioPermission,
    cameraPermission,
    storagePermission,
    locationPermission,
  } = useDevicePermissions();

  useEffect(() => {
    const allGranted =
      audioPermission === 'granted' &&
      cameraPermission === 'granted' &&
      storagePermission === 'granted' &&
      locationPermission === 'granted';

    if (allGranted) {
      StackActions.replace('home');
    }
  }, [
    audioPermission,
    cameraPermission,
    storagePermission,
    locationPermission,
  ]);

  return (
    <>
      <CheckPermission />
    </>
  );
}
