import React, { useState, useCallback, useEffect } from 'react';
import {
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
  StatusBar,
  useColorScheme,
  ActivityIndicator,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@react-native-vector-icons/ionicons';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../localStroage/asyncStorage';

type ThemePreference = 'system' | 'light' | 'dark';
type ColorScheme = 'light' | 'dark';
type Resolution = 'auto' | '720p' | '1080p' | '4k';

const themeOptions: { value: ThemePreference; label: string }[] = [
  { value: 'system', label: 'System Default' },
  { value: 'light', label: 'Light' },
  { value: 'dark', label: 'Dark' },
];
const resolutionOptions: { value: Resolution; label: string }[] = [
  { value: 'auto', label: 'Auto (Device Best)' },
  { value: '720p', label: '720p (HD)' },
  { value: '1080p', label: '1080p (Full HD)' },
  { value: '4k', label: '4K (Ultra HD)' },
];

const SettingRow = React.memo(
  ({
    title,
    subtitle,
    onPress,
    rightComponent,
    styles,
  }: {
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightComponent?: React.ReactNode;
    styles: ReturnType<typeof getStyles>;
  }) => (
    <Pressable style={styles.settingRow} onPress={onPress}>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {rightComponent}
    </Pressable>
  ),
);

const RadioSelectionGroup = React.memo(
  ({
    title,
    options,
    selectedValue,
    onSelect,
    styles,
  }: {
    title: string;
    options: { value: string; label: string }[];
    selectedValue: string;
    onSelect: (value: any) => void;
    styles: ReturnType<typeof getStyles>;
  }) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {options.map(option => (
        <SettingRow
          key={option.value}
          title={option.label}
          onPress={() => onSelect(option.value)}
          styles={styles}
          rightComponent={
            <Ionicons
              name={
                selectedValue === option.value
                  ? 'radio-button-on'
                  : 'radio-button-off'
              }
              size={24}
              style={styles.icon}
            />
          }
        />
      ))}
    </View>
  ),
);

export default function Settings() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();

  const [themePref, setThemePref] = useState<ThemePreference>('system');
  const [resolution, setResolution] = useState<Resolution>('auto');
  const [isLoading, setIsLoading] = useState(true);

  const systemTheme = useColorScheme() ?? 'light';
  const activeTheme: ColorScheme =
    themePref === 'system' ? systemTheme : themePref;
  const styles = getStyles(activeTheme);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedTheme = (await AsyncStorage.getItem(
          STORAGE_KEYS.THEME_KEY,
        )) as ThemePreference;
        const savedResolution = (await AsyncStorage.getItem(
          STORAGE_KEYS.RESOLUTION_KEY,
        )) as Resolution;

        if (savedTheme) setThemePref(savedTheme);
        if (savedResolution) setResolution(savedResolution);
      } catch (e) {
        console.error('Failed to load settings.', e);
      } finally {
        setIsLoading(false);
      }
    };
    loadSettings();
  }, []);

  const handleSelectTheme = useCallback(async (newTheme: ThemePreference) => {
    setThemePref(newTheme);
    await AsyncStorage.setItem(STORAGE_KEYS.THEME_KEY, newTheme);
  }, []);

  const handleSelectResolution = useCallback(async (newRes: Resolution) => {
    setResolution(newRes);
    await AsyncStorage.setItem(STORAGE_KEYS.RESOLUTION_KEY, newRes);
  }, []);

  if (isLoading) {
    return (
      <ActivityIndicator
        size="large"
        style={{ flex: 1, backgroundColor: '#000' }}
      />
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar
        barStyle={activeTheme === 'dark' ? 'light-content' : 'dark-content'}
      />
      <View style={styles.header}>
        <Pressable
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons
            name="arrow-back-outline"
            size={32}
            style={styles.headerIcon}
          />
        </Pressable>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={{ paddingBottom: insets.bottom }}
      >
        <RadioSelectionGroup
          title="Appearance"
          options={themeOptions}
          selectedValue={themePref}
          onSelect={handleSelectTheme}
          styles={styles}
        />

        <RadioSelectionGroup
          title="Video Settings"
          options={resolutionOptions}
          selectedValue={resolution}
          onSelect={handleSelectResolution}
          styles={styles}
        />
      </ScrollView>
    </View>
  );
}

const getStyles = (theme: ColorScheme) => {
  const isDark = theme === 'dark';
  const colors = {
    background: isDark ? '#000' : '#F2F2F7',
    header: isDark ? '#1C1C1E' : '#FFF',
    text: isDark ? '#FFF' : '#000',
    subtitle: '#888',
    separator: isDark ? '#333' : '#E5E5EA',
    primary: isDark ? '#4CAF50' : '#007AFF',
    rowBackground: isDark ? '#1C1C1E' : '#FFF',
  };

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderBottomColor: colors.separator,
      backgroundColor: colors.header,
    },
    backButton: {
      padding: 8,
      marginLeft: -8,
    },
    headerTitle: {
      flex: 1,
      color: colors.text,
      fontSize: 18,
      fontWeight: '600',
      textAlign: 'center',
    },
    headerIcon: {
      color: colors.text,
    },
    placeholder: {
      width: 40,
    },
    scrollView: {
      flex: 1,
    },
    section: {
      marginTop: 24,
    },
    sectionTitle: {
      color: colors.primary,
      fontSize: 16,
      fontWeight: '600',
      paddingHorizontal: 16,
      paddingBottom: 8,
    },
    settingRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderBottomColor: colors.separator,
      backgroundColor: colors.rowBackground,
    },
    settingContent: {
      flex: 1,
    },
    settingTitle: {
      color: colors.text,
      fontSize: 16,
    },
    settingSubtitle: {
      color: colors.subtitle,
      fontSize: 12,
      marginTop: 2,
    },
    icon: {
      color: colors.primary,
    },
  });
};
