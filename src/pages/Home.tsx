import { View, ActivityIndicator, StyleSheet, Alert } from 'react-native';
import { useDevicePermissions } from '../hooks/useDevicePermissions';
import CheckPermission from '../components/CheckPermission';
import LocationCard from '../components/LocationCard';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ShutterButton from '../components/ShutterButton';
import { useIsForeground } from '../hooks/useIsForeground';
import {
  NavigationProp,
  useIsFocused,
  useNavigation,
} from '@react-navigation/core';
import { Camera, useCameraDevice, VideoFile } from 'react-native-vision-camera';
import { useEffect, useMemo, useRef, useState } from 'react';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import CameraHeader from '../components/CameraHeader';
import { Resolution, RootStack } from '../types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../localStroage/asyncStorage';

export default function Home() {
  const {
    audioPermission,
    cameraPermission,
    storagePermission,
    locationPermission,
    isLoading: permissionsLoading,
  } = useDevicePermissions();

  const { bottom } = useSafeAreaInsets();
  const [flashMode, setFlashMode] = useState<'on' | 'off'>('off');
  const [cameraPosition, setCameraPosition] = useState<'back' | 'front'>(
    'back',
  );
  const [enableLocation, setEnableLocation] = useState(true);
  const [recording, setRecording] = useState(false);

  const [selectedResolution, setSelectedResolution] =
    useState<Resolution>('auto');
  const [isSettingsLoading, setIsSettingsLoading] = useState(true);
  const [hasAllPermissions, setHasAllPermissions] = useState(false);

  const isFocussed = useIsFocused();
  const isForeground = useIsForeground();
  const isActive = isFocussed && isForeground;

  const device = useCameraDevice(cameraPosition);
  const cameraRef = useRef<Camera>(null);
  const { navigate } = useNavigation<NavigationProp<RootStack>>();

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedRes = await AsyncStorage.getItem(
          STORAGE_KEYS.RESOLUTION_KEY,
        );

        if (savedRes) setSelectedResolution(savedRes as Resolution);
      } catch (e) {
        console.error('Failed to load settings.', e);
      } finally {
        setIsSettingsLoading(false);
      }
    };

    if (isFocussed) {
      loadSettings();
    }
  }, [isFocussed]);

  const videoFormat = useMemo(() => {
    if (device?.formats == null) return undefined;

    if (selectedResolution === 'auto') {
      return device.formats.reduce((prev, curr) => {
        const prevPixels = prev.videoWidth * prev.videoHeight;
        const currPixels = curr.videoWidth * curr.videoHeight;
        return currPixels > prevPixels ? curr : prev;
      });
    }

    const targetHeight = parseInt(selectedResolution.replace('p', ''));
    const matchingFormats = device.formats.filter(
      f => f.videoHeight === targetHeight,
    );

    if (matchingFormats.length > 0) {
      return matchingFormats.reduce((prev, curr) => {
        return curr.maxFps > prev.maxFps ? curr : prev;
      });
    }
    return undefined;
  }, [device, selectedResolution]);

  const videoBitRate = useMemo(() => {
    switch (selectedResolution) {
      case '720p':
        return 2.3;
      case '1080p':
        return 5;
      case '4k':
        return 20;
      case 'auto':
      default:
        return 8;
    }
  }, [selectedResolution]);

  async function onRecordingFinished(video: VideoFile) {
    setRecording(false);
    try {
      await CameraRoll.save(video.path, {
        type: 'video',
        album: 'vCam',
      });
      Alert.alert('Success', `Video saved to gallery.`);
    } catch (error) {
      console.error(error);
      Alert.alert('Error', 'Failed to save video to gallery.');
    }
  }

  function startRecording() {
    if (device == null || cameraRef.current == null) return;
    setRecording(true);
    cameraRef.current.startRecording({
      onRecordingFinished: onRecordingFinished,
      flash: device.hasFlash && flashMode === 'on' ? 'on' : 'off',
      onRecordingError: error => {
        console.error('Recording error: ', error);
        setRecording(false);
      },
    });
  }

  async function stopRecording() {
    await cameraRef.current?.stopRecording();
  }

  function toggleCamera() {
    setCameraPosition(prev => (prev === 'back' ? 'front' : 'back'));
  }

  function toggleLocation() {
    setEnableLocation(prev => !prev);
  }

  function toggleFlash() {
    setFlashMode(prev => (prev === 'on' ? 'off' : 'on'));
  }

  function navigateToSettings() {
    navigate('settings');
  }

  const isLoading = permissionsLoading || isSettingsLoading;

  useEffect(() => {
    const allGranted =
      audioPermission === 'granted' &&
      cameraPermission === 'granted' &&
      storagePermission === 'granted' &&
      locationPermission === 'granted';

    setHasAllPermissions(allGranted);
  }, [
    audioPermission,
    cameraPermission,
    storagePermission,
    locationPermission,
  ]);

  if (isLoading) {
    return <ActivityIndicator size="large" style={styles.loadingIndicator} />;
  }

  if (!hasAllPermissions) {
    return <CheckPermission />;
  }

  return (
    <>
      <CameraHeader
        enableLocation={enableLocation}
        toggleLocation={toggleLocation}
        toggleCamera={toggleCamera}
        toggleFlash={toggleFlash}
        flashMode={flashMode}
        cameraPosition={cameraPosition}
        hasFlash={device?.hasFlash}
        recording={recording}
        navigateToSettings={navigateToSettings}
      />
      {device && (
        <Camera
          device={device}
          isActive={isActive}
          style={StyleSheet.absoluteFill}
          video={true}
          audio={true}
          videoBitRate={videoBitRate}
          ref={cameraRef}
          lowLightBoost={device.supportsLowLightBoost}
          outputOrientation="device"
          pixelFormat="yuv"
          format={videoFormat}
        />
      )}
      <View style={styles.container}>
        <View style={[styles.bottomControls, { bottom: bottom + 12 }]}>
          <View
            style={{
              display:
                enableLocation && locationPermission === 'granted'
                  ? 'flex'
                  : 'none',
            }}
          >
            <LocationCard />
          </View>
          <ShutterButton
            onStartRecording={startRecording}
            onStopRecording={stopRecording}
            recording={recording}
          />
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  loadingIndicator: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  container: {
    flex: 1,
  },
  bottomControls: {
    position: 'absolute',
    left: 8,
    right: 8,
    gap: 12,
    paddingHorizontal: 8,
  },
});
