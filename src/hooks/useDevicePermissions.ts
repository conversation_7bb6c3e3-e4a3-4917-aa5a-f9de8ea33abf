import { useState, useCallback } from 'react';
import { Alert, Linking, Platform } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import {
  PERMISSIONS,
  RESULTS,
  checkMultiple,
  request,
  Permission,
} from 'react-native-permissions';
import {
  loadPermissionsFromStorage,
  savePermissionsToStorage,
} from '../utils/helper';

type PermissionStatus = (typeof RESULTS)[keyof typeof RESULTS];
export interface PermissionState {
  camera: PermissionStatus;
  audio: PermissionStatus;
  location: PermissionStatus;
  storage: PermissionStatus;
}
type PermissionKey = keyof PermissionState;

const PLATFORM_PERMISSIONS: Record<PermissionKey, Permission> = {
  camera: Platform.select({
    ios: PERMISSIONS.IOS.CAMERA,
    android: PERMISSIONS.ANDROID.CAMERA,
  })!,
  audio: Platform.select({
    ios: PERMISSIONS.IOS.MICROPHONE,
    android: PERMISSIONS.ANDROID.RECORD_AUDIO,
  })!,
  location: Platform.select({
    ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
    android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
  })!,
  storage: Platform.select({
    ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
    android:
      Number(Platform.Version) >= 33
        ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
        : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
  })!,
};

const defaultPermissions: PermissionState = {
  camera: 'denied',
  audio: 'denied',
  location: 'denied',
  storage: 'denied',
};

export function useDevicePermissions() {
  const [permissions, setPermissions] =
    useState<PermissionState>(defaultPermissions);
  const [isLoading, setIsLoading] = useState(true);

  const checkAllPermissions = useCallback(async () => {
    const statuses = await checkMultiple(Object.values(PLATFORM_PERMISSIONS));

    const newPermissions: PermissionState = {
      camera: statuses[PLATFORM_PERMISSIONS.camera],
      audio: statuses[PLATFORM_PERMISSIONS.audio],
      location: statuses[PLATFORM_PERMISSIONS.location],
      storage: statuses[PLATFORM_PERMISSIONS.storage],
    };

    setPermissions(newPermissions);
    await savePermissionsToStorage(newPermissions);
    return newPermissions;
  }, []);

  const requestPermission = useCallback(
    async (permission: PermissionKey) => {
      const permissionString = PLATFORM_PERMISSIONS[permission];
      const result = await request(permissionString);

      if (result === RESULTS.BLOCKED) {
        Alert.alert(
          `Permission Blocked`,
          `Please enable the ${permission} permission in your device settings.`,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => Linking.openSettings() },
          ],
        );
      }

      await checkAllPermissions();
    },
    [checkAllPermissions],
  );

  useFocusEffect(
    useCallback(() => {
      const initialize = async () => {
        setIsLoading(true);
        const storedPermissions = await loadPermissionsFromStorage();
        if (storedPermissions) {
          setPermissions(storedPermissions);
        }
        await checkAllPermissions();
        setIsLoading(false);
      };

      initialize();
    }, [checkAllPermissions]),
  );

  return {
    isLoading,
    cameraPermission: permissions.camera,
    audioPermission: permissions.audio,
    locationPermission: permissions.location,
    storagePermission: permissions.storage,
    requestCameraPermission: () => requestPermission('camera'),
    requestAudioPermission: () => requestPermission('audio'),
    requestLocationPermission: () => requestPermission('location'),
    requestStoragePermission: () => requestPermission('storage'),
  };
}
